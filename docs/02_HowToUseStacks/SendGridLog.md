# SendGridLog

## Overview

At Mynavi, it is recommended to use SendGrid for sending emails.

This document describes the implementation of SendGrid event logging using AWS services. The solution enables collecting, storing, and analyzing SendGrid email events in a cost-effective way.

SendGrid posts event data to API Gateway based on the SendGrid Webhook configuration.
Logs are stored in S3 in Parquet format to reduce query costs when querying with <PERSON>.

## Architecture Flow

The flow is as follows: SendGrid → API Gateway → Kinesis Firehose → S3 ← Athena.
![](images/SendGridLog-Flow.png)

**Key Components:**

- **SendGrid**: Email service that generates event data (bounces, deliveries, opens, etc.)
- **Amazon Cognito** :
  - Manages OAuth 2.0 authentication for SendGrid webhooks
  - Provides secure token-based authentication
  - Components:
    - User Pool: Manages webhook application credentials
    - Resource Server: Defines custom scopes for webhook access
    - App Client: Used by SendGrid for OAuth token requests
    - Domain: Hosts the OAuth endpoints
- **API Gateway**: Receives webhook events from SendGrid
- **Kinesis Firehose**: Processes and transforms data into Parquet format
- **S3**: Storage destination for log data
- **Athena**: SQL query service for analyzing logs
- **CloudWatch Alarms**:
  - Monitors API Gateway metrics:
    - 4XX errors (authentication/validation failures)
    - 5XX errors (system/integration failures)
  - Integration with SNS for notifications

## Implementation Steps

### 1. Configure SendGrid Webhook

1. Log into SendGrid dashboard
2. Navigate to Settings → Mail Settings → Event Webhook
3. Configure the webhook:
   - HTTP Post URL: Your API Gateway endpoint
   - Select events to track (bounces, deliveries, opens, etc.)
   - Enable OAuth2.0 webhook for security
     - **Client ID**: Get from Cognito App Client settings
     - **Client Secret**: Get from Cognito App Client settings
     - **Token URL**: `https://{your-cognito-domain}.auth.{region}.amazoncognito.com/oauth2/token`
       ![](images/SendGridLog-OAuth-Webhook.png)

### 2. Data Storage

When an event is received from SendGrid, a log will be stored in Parquet format in the S3 bucket.

### 3. Querying Using Athena

1. Setting query Editor
   ![](images/SendGridLog-Setting-QueryEditor.png)
   Specify Query result location
   ![](images/SendGridLog-QueryResultLoc.png)
2. Choose Athena Database
   ![](images/SendGridLog-Database.png)
3. Sample Queries:
   ```sql
   select * from "dev21blea_xxxx_sendgrid_logs"."apigateway_sendgrid";
   ```
   ![](images/SendGridLog-QueryResult.png)

## Monitoring and Alerting:

### CloudWatch Alarms

1. **API Gateway 4XX errors**
   - Threshold: > 10 errors in 5 minutes
   - Action: SNS notification
2. **API Gateway 5XX errors**
   - Threshold: > 10 error in 5 minutes
   - Action: SNS notification

**4xx**
![](images/SendGridLog-4xx-Alarm.png)
**Message**
![](images/SendGridLog-4xx-Message.png)

**5xx**
![](images/SendGridLog-5xx-Alarm.png)
**Message**
![](images/SendGridLog-5xx-Message.png)

### Error Handling

In case SendGrid fails to send the event to API Gateway (4xx, 5xx errors):

- SendGrid will retry the POST request until it receives a 2xx response or the maximum time has elapsed
- All events are retried at increasing intervals for up to 24 hours after the event occurs
- This is a rolling 24-hour period, meaning new failing events will receive their own 24-hour retry period

Reference:

- [SendGrid Event Webhook Documentation](https://www.twilio.com/docs/sendgrid/for-developers/tracking-events/getting-started-event-webhook)
- [SendGrid Event Webhook Retry Logic](https://www.twilio.com/docs/sendgrid/for-developers/tracking-events/getting-started-event-webhook#retry-logic)

## Configuration

### `SendGridLogParams` Sample Code

```typescript
export const SendGridLogParams: inf.ISendGridLogParam[] = [
  {
    s3BucketArn: '', // If you have an existing bucket, enter the ARN here. If you don't have one, leave it blank, and a new bucket will be created.
    athenaDateRangeFrom: '2025-03-18', // Replace YYYY-MM-DD with the starting date that you want to use
    suffix: 'xxxx', // 4 character string
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
  {
    s3BucketArn: '', // If you have an existing bucket, enter the ARN here. If you don't have one, leave it blank, and a new bucket will be created.
    athenaDateRangeFrom: '2025-03-18', // Replace YYYY-MM-DD with the starting date that you want to use
    suffix: 'abcdef', // 4 character string
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
];
```
