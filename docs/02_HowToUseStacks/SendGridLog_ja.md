# SendGridLog

## 概要

Mynavi では、メール送信に SendGrid を使用することが推奨されています。

このドキュメントでは、AWS サービスを使用した SendGrid イベントログの実装について説明します。このソリューションにより、SendGrid のメールイベントを効率的に収集、保存、分析することができます。

SendGrid は、SendGrid Webhook の設定に基づいてイベントデータを API Gateway に送信します。
ログは Athena でクエリする際のコスト削減のため、S3 に Parquet 形式で保存されます。

## アーキテクチャフロー

フローは次の通りです：SendGrid → API Gateway → Kinesis Firehose → S3 ← Athena
![](images/SendGridLog-Flow-ja.png)

**主要コンポーネント：**

- **SendGrid**：イベントデータ（bounces、deliveries、opens など）を生成するメール配信サービス
- **Amazon Cognito**：
  - SendGrid ウェブフックの OAuth 2.0 認証を管理
  - 安全なトークンベースの認証を提供
  - コンポーネント：
    - ユーザープール：ウェブフックアプリケーション認証情報を管理
    - リソースサーバー：ウェブフックアクセス用のカスタムスコープを定義
    - アプリクライアント：SendGrid が OAuth トークンをリクエストするために使用
    - ドメイン：OAuth エンドポイントをホスト
- **API Gateway**：SendGrid からウェブフックイベントを受信
- **Kinesis Firehose**：データを処理し Parquet 形式に変換
- **S3**：ログデータの保存先
- **Athena**：ログ分析用の SQL クエリサービス
- **CloudWatch Alarms**：
  - API Gateway メトリクスを監視：
    - 4XX エラー（認証/検証の失敗）
    - 5XX エラー（システム/統合の失敗）
  - SNS による通知連携

## 実装手順

### 1. SendGrid Webhook の設定

1. SendGrid ダッシュボードにログイン
2. Settings → Mail Settings → Event Webhook に移動
3. ウェブフックを設定：
   - HTTP Post URL：API Gateway エンドポイント
   - 追跡するイベント（bounces、deliveries、opens など）を選択
   - セキュリティのための OAuth2.0 ウェブフックを有効化
     - **クライアント ID**：Cognito App Client 設定から取得
     - **クライアントシークレット**：Cognito App Client 設定から取得
     - **トークン URL**：`https://{あなたのCognitoドメイン}.auth.{リージョン}.amazoncognito.com/oauth2/token`
       ![](images/SendGridLog-OAuth-Webhook.png)

### 2. データ保管

SendGrid からイベントを受信すると、ログは S3 バケットに Parquet 形式で保存されます。

### 3. Athena を使用したクエリ

1. クエリエディター
   ![](images/SendGridLog-Setting-QueryEditor.png)
   クエリ結果の保存先を指定
   ![](images/SendGridLog-QueryResultLoc.png)
2. Athena データベースの選択
   ![](images/SendGridLog-Database.png)
3. サンプルクエリ：
   ```sql
   select * from "dev21blea_xxxx_sendgrid_logs"."apigateway_sendgrid";
   ```
   ![](images/SendGridLog-QueryResult.png)

## 監視とアラート：

### CloudWatch アラーム

1. **API Gateway 4XX エラー**
   - 閾値: 5 分間に 10 件を超えるエラー
   - アクション: SNS 通知
2. **API Gateway 5XX エラー**
   - 閾値: 5 分間に 10 件を超えるエラー
   - アクション: SNS 通知

**4xx**
![](images/SendGridLog-4xx-Alarm.png)
**メッセージ**
![](images/SendGridLog-4xx-Message.png)

**5xx**
![](images/SendGridLog-5xx-Alarm.png)
**メッセージ**
![](images/SendGridLog-5xx-Message.png)

### エラー処理

SendGrid が API Gateway にイベントを送信できない場合（4xx、5xx エラー）：

- SendGrid は 2xx レスポンスを受信するか、最大時間が経過するまで POST リクエストをリトライします
- すべてのイベントは、イベント発生後最大 24 時間まで、増加する間隔でリトライされます
- これは 24 時間のロールイング期間であり、新しい失敗イベントにはそれぞれ 24 時間のリトライ期間が与えられます
- 参考：[SendGrid Event Webhook Documentation](https://www.twilio.com/docs/sendgrid/for-developers/tracking-events/getting-started-event-webhook)

## 設定

### `SendGridLogParams` サンプルコード

```typescript
export const SendGridLogParams: inf.ISendGridLogParam[] = [
  {
    s3BucketArn: '', // 既存のバケットがある場合はARNを入力。ない場合は空白のままにすると、新しいバケットが作成されます。
    athenaDateRangeFrom: '2025-03-18', // 使用したい開始日をYYYY-MM-DD形式で置き換えてください
    suffix: 'xxxx', // 4文字の文字列
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
  {
    s3BucketArn: '', // 既存のバケットがある場合はARNを入力。ない場合は空白のままにすると、新しいバケットが作成されます。
    athenaDateRangeFrom: '2025-03-18', // 使用したい開始日をYYYY-MM-DD形式で置き換えてください
    suffix: 'abcdef', // 4文字の文字列
    createAlarm: true,
    notifyEmail: '<EMAIL>',
  },
];
```
