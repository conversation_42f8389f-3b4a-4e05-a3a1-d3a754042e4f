import * as cdk from 'aws-cdk-lib';
import { DbAuroraStack } from '../lib/stack/db-aurora-stack';
import { WafCfStack } from '../lib/stack/waf-cloudfront-stack';
import { WafAlbStack } from '../lib/stack/waf-alb-stack';
import { ElastiCacheStack } from '../lib/stack/elasticache-stack';
import * as fs from 'fs';
import { IConfig } from '../params/interface';
import { OpenSearchStack } from '../lib/stack/opensearch-stack';
import { BackupVaultStack } from '../lib/stack/backup-vault-stack';
import { BackupPlanStack } from '../lib/stack/backup-plan-stack';
import { MonitorStack } from '../lib/stack/monitor-stack';
import { EcsAppStack } from '../lib/stack/ecs-app-stack';
import { CloudfrontStack } from '../lib/stack/cloudfront-stack';
import { OidcStack } from '../lib/stack/oidc-stack';
import { InfraResourcesPipelineStack } from '../lib/stack/pipeline-infraresources-stack';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import { EfsStack } from '../lib/stack/efs-stack';
import { KMSStack } from '../lib/stack/kms-stack';
import { SendGridLogStack } from '../lib/stack/sendgrid-log-stack';
import { ProtectResourceStack } from '../lib/stack/protect-resource-stack';

const app = new cdk.App();

// ----------------------- Load context variables ------------------------------
// This context need to be specified in args
const argContext = 'environment';
const envKey = app.node.tryGetContext(argContext);
if (envKey == undefined)
  throw new Error(`Please specify environment with context option. ex) cdk deploy -c ${argContext}=dev`);
//Read Typescript Environment file
const TsEnvPath = './params/' + envKey + '.ts';
if (!fs.existsSync(TsEnvPath)) throw new Error(`Can't find a ts environment file [../params/` + envKey + `.ts]`);

//ESLintではrequireの利用が禁止されているため除外コメントを追加
//https://github.com/mynavi-group/csys-infra-baseline-environment-on-aws-change-homemade/issues/29#issuecomment-**********
const config: IConfig = require('../params/' + envKey);

// Add envName to Stack for avoiding duplication of Stack names.
const pjPrefix = config.Env.envName + 'BLEA';

// ----------------------- Environment variables for stack ------------------------------
// Default environment
const procEnvDefault = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION,
};

// Define account id and region from context.
// If "env" isn't defined on the environment variable in context, use account and region specified by "--profile".
function getProcEnv() {
  if (config.Env.account && config.Env.region) {
    return {
      account: config.Env.account,
      region: config.Env.region,
    };
  } else {
    return procEnvDefault;
  }
}

// ----------------------- Guest System Stacks ------------------------------

// Slack Notifier
const workspaceId = config.NotifierParam.workspaceId;
const channelIdMon = config.NotifierParam.channelIdMon;

const shareResources = new ShareResourcesStack(app, `${pjPrefix}-ShareResources`, {
  pjPrefix,
  notifyEmail: config.NotifierParam.monitoringNotifyEmail,
  domainPrefix: `${pjPrefix}`.toLowerCase(),
  workspaceId: workspaceId,
  channelId: channelIdMon,
  ...config.CognitoParam,
  myVpcCidr: config.VpcParam.cidr,
  myVpcMaxAzs: config.VpcParam.maxAzs,
  myVpcNatGateways: config.VpcParam.natGateways,
  myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
  env: getProcEnv(),
  terminationProtection: config.ShareResourcesParam.stackTerminationProtection,
});

// InfraResources
new InfraResourcesPipelineStack(app, `${pjPrefix}-Pipeline`, {
  ...config.InfraResourcesPipelineParam,
  pipelineBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
  envKey,
  env: getProcEnv(),
  appKey: shareResources.appKey,
  terminationProtection: config.InfraResourcesPipelineParam.stackTerminationProtection,
});

// const wafCloudfront = new WafCfStack(app, `${pjPrefix}-WafCloudfront`, {
//   scope: 'CLOUDFRONT',
//   env: {
//     account: getProcEnv().account,
//     region: 'us-east-1',
//   },
//   crossRegionReferences: true,
//   ...config.WafParam,
//   wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
//   logRemovalPolicyParam: config.LogRemovalPolicyParam,
//   csirtWAFParam: config.CSIRTWAFParamCF,
//   terminationProtection: config.WafParam.stackTerminationProtection,
// });

// new OidcStack(app, `${pjPrefix}-OIDC`, {
//   OrganizationName: config.OidcParam.OrganizationName,
//   RepositoryNames: config.OidcParam.RepositoryNames,
//   env: getProcEnv(),
//   terminationProtection: config.OidcParam.stackTerminationProtection,
// });

const ecs = new EcsAppStack(app, `${pjPrefix}-ECS`, {
  myVpc: shareResources.myVpc,
  appKey: shareResources.appKey,
  alarmTopic: shareResources.alarmTopic,
  prefix: pjPrefix,
  albParam: config.AlbParam,
  albBgParam: config.AlbBgParam,
  ecsFrontTasks: config.EcsFrontTasks,
  ecsBackTasks: config.EcsBackTasks,
  ecsFrontBgTasks: config.EcsFrontBgTasks,
  ecsBackBgTasks: config.EcsBackBgTasks,
  env: getProcEnv(),
  crossRegionReferences: true,
  bastionParams: config.BastionParam,
  otherRemovalPolicyParam: config.OtherRemovalPolicyParam,
  logRemovalPolicyParam: config.LogRemovalPolicyParam,
  albAccessLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
  pipelineSourceBucketLifeCycleRules: config.pipelineSourceBucketLifeCycleRules,
  terminationProtection: config.ECSAppParam.stackTerminationProtection,
});

// const wafAlb = new WafAlbStack(app, `${pjPrefix}-WafAlb`, {
//   scope: 'REGIONAL',
//   associations: [ecs.app.frontAlb.appAlb.loadBalancerArn],
//   env: getProcEnv(),
//   crossRegionReferences: true,
//   ...config.WafAlbParam,
//   wafLogBucketLifecycleRules: config.s3AuditLogLifecycleRules,
//   logRemovalPolicyParam: config.LogRemovalPolicyParam,
//   csirtWAFParam: config.CSIRTWAFParamALB,
//   terminationProtection: config.WafAlbParam.stackTerminationProtection,
// });

// const cloudfront = new CloudfrontStack(app, `${pjPrefix}-Cloudfront`, {
//   pjPrefix: pjPrefix,
//   webAcl: wafCloudfront.webAcl,
//   CertificateIdentifier: config.CertificateIdentifier,
//   cloudFrontParam: config.CloudFrontParam,
//   appAlbs: [ecs.app.frontAlb.appAlb],
//   preSharedKey: wafAlb.preSharedKey,
//   logRemovalPolicyParam: config.LogRemovalPolicyParam,
//   webContentBucketRemovalPolicyParam: config.OtherRemovalPolicyParam,
//   env: getProcEnv(),
//   crossRegionReferences: true,
//   accessLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
//   terminationProtection: config.CloudFrontParam.stackTerminationProtection,
// });
// cloudfront.addDependency(wafAlb);
// Aurora
const dbCluster = new DbAuroraStack(app, `${pjPrefix}-DBAurora`, {
  // pjPrefix: pjPrefix,
  vpc: shareResources.myVpc,
  vpcSubnets: shareResources.myVpc.selectSubnets({
    subnetGroupName: 'Protected',
  }),
  appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
  bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
  appKey: shareResources.appKey,
  alarmTopic: shareResources.alarmTopic,
  ...config.AuroraParam,
  env: getProcEnv(),
  terminationProtection: config.AuroraParam.stackTerminationProtection,
});

// new MonitorStack(app, `${pjPrefix}-MonitorStack`, {
//   pjPrefix: `${pjPrefix}`,
//   dashboardName: `${pjPrefix}-ECSApp`,
//   cfDistributionId: cloudfront.cfDistributionId,
//   albFullName: ecs.app.frontAlb.appAlb.loadBalancerFullName,
//   appTargetGroupNames: ecs.app.frontAlb.AlbTgs.map((AlbTg) => AlbTg.lbForAppTargetGroup.targetGroupName),
//   albTgUnHealthyHostCountAlarms: ecs.app.frontAlb.AlbTgs.map((AlbTg) => AlbTg.albTgUnHealthyHostCountAlarm),
//   ecsClusterName: ecs.app.ecsCommon.ecsCluster.clusterName,
//   ecsAlbServiceNames: ecs.app.frontEcsApps.map((ecsAlbApp) => ecsAlbApp.ecsServiceName),
//   ecsInternalServiceNames: ecs.app.backEcsApps.map((ecsInternalApp) => ecsInternalApp.ecsServiceName),
//   dbClusterName: dbCluster.dbClusterName,
//   // AutoScaleはCDK外で管理のため、固定値を修正要で設定
//   ecsScaleOnRequestCount: 50,
//   ecsTargetUtilizationPercent: 10000,
//   env: getProcEnv(),
//   terminationProtection: config.MonitorParam.stackTerminationProtection,
// });

// new OpenSearchStack(app, `${pjPrefix}-OpenSearch`, {
//   vpc: shareResources.myVpc,
//   appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
//   bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
//   openSearchType: config.OpensearchTypeParam,
//   openSearchProps: config.OpensearchParam,
//   env: getProcEnv(),
//   terminationProtection: config.OpensearchParam.stackTerminationProtection,
// });

// const elasticCache = new ElastiCacheStack(app, `${pjPrefix}-ElastiCache`, {
//   pjPrefix,
//   elastiCacheType: config.ElastiCacheTypeParam,
//   myVpc: shareResources.myVpc,
//   appKey: shareResources.appKey,
//   alarmTopic: shareResources.alarmTopic,
//   appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
//   bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
//   ElastiCacheParam: config.ElastiCacheParam,
//   logGroupRemovalPolicy: config.LogRemovalPolicyParam?.removalPolicy,
//   env: getProcEnv(),
//   terminationProtection: config.ElastiCacheParam.stackTerminationProtection,
// });

// const efs = new EfsStack(app, `${pjPrefix}-Efs`, {
//   pjPrefix,
//   vpc: shareResources.myVpc,
//   appKey: shareResources.appKey,
//   appServerSecurityGroup: ecs.app.backEcsApps[0].securityGroupForFargate,
//   bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
//   ...config.EfsParam,
//   env: getProcEnv(),
//   terminationProtection: config.EfsParam.stackTerminationProtection,
// });

// // AWS Backupを利用する場合は以下をif文も含めてコメントインする。
// // 環境別パラメータファイル内でbackupDisasterRecoveryをtrueに設定するとDR用リージョンにクロスリージョンコピーされる。
// // falseであればDRリージョンにクロスリージョンレプリケーションされず東京リージョンのみのデプロイとなる。

// const backupVault = new BackupVaultStack(app, `${pjPrefix}-BackupVault`, {
//   env: getProcEnv(),
//   appKey: shareResources.appKey,
//   terminationProtection: config.BackupParam.stackTerminationProtection,
// });

// if (config.BackupParam.backupDisasterRecovery) {
//   // DR用リージョンにKMSキーを作成
//   const appKeyDRRegion = new KMSStack(app, `${pjPrefix}-AppKeyDRRegion`, {
//     pjPrefix,
//     env: {
//       account: getProcEnv().account,
//       region: config.DRRegionParam.region,
//     },
//     removalPolicyParam: config.LogRemovalPolicyParam,
//     kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
//     crossRegionReferences: true,
//     terminationProtection: config.BackupParam.stackTerminationProtection,
//   });

//   // DR用リージョンにバックアップボールトを作成
//   const backupVaultDRRegion = new BackupVaultStack(app, `${pjPrefix}-BackupVaultDRRegion`, {
//     env: {
//       account: getProcEnv().account,
//       region: config.DRRegionParam.region,
//     },
//     appKey: appKeyDRRegion.kmsKey,
//     crossRegionReferences: true,
//     terminationProtection: config.BackupParam.stackTerminationProtection,
//   });

//   // DR用リージョンと東京リージョンに作成されたバックアップボールトを指定して、バックアッププランを作成
//   new BackupPlanStack(app, `${pjPrefix}-BackupPlan`, {
//     env: getProcEnv(),
//     vault: backupVault.vault,
//     secondaryVault: backupVaultDRRegion.vault,
//     backupSchedule: config.BackupParam.backupSchedule,
//     retentionPeriod: config.BackupParam.retentionPeriod,
//     crossRegionReferences: true,
//     terminationProtection: config.BackupParam.stackTerminationProtection,
//   });
// } else {
//   // 東京リージョンに作成されたバックアップボールトを指定して、バックアッププランを作成
//   new BackupPlanStack(app, `${pjPrefix}-BackupPlan`, {
//     env: getProcEnv(),
//     vault: backupVault.vault,
//     backupSchedule: config.BackupParam.backupSchedule,
//     retentionPeriod: config.BackupParam.retentionPeriod,
//     terminationProtection: config.BackupParam.stackTerminationProtection,
//   });
// }

// new SendGridLogStack(app, `${pjPrefix}-SendGridLog`, {
//   env: getProcEnv(),
//   pjPrefix: pjPrefix.toLowerCase(),
//   sendGridLogParams: config.SendGridLogParams,
//   logRemovalPolicyParam: config.LogRemovalPolicyParam,
//   lifecycleRules: config.s3AuditLogLifecycleRules,
//   terminationProtection: config.SendGridLog.stackTerminationProtection,
// });

// --------------------------------- Protect Resource Stack  -------------------------------------
// const protectResourceStack = new ProtectResourceStack(app, `${pjPrefix}-ProtectResource`, {
//   pjPrefix: pjPrefix,
//   env: getProcEnv(),
//   stackResourceProtection: {
//     [dbCluster.stackName]: true,
//     [ecs.stackName]: true,
//     [elasticCache.stackName]: true,
//     [efs.stackName]: true,
//   },
// });
// protectResourceStack.addDependency(dbCluster);
// protectResourceStack.addDependency(ecs);
// protectResourceStack.addDependency(elasticCache);
// protectResourceStack.addDependency(efs);
// --------------------------------- Tagging  -------------------------------------

// Tagging "Environment" tag to all resources in this app
const envTagName = 'Environment';
cdk.Tags.of(app).add(envTagName, config.Env.envName);
