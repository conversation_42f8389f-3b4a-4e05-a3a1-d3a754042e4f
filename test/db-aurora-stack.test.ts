import * as cdk from 'aws-cdk-lib';
import { Template, Match } from 'aws-cdk-lib/assertions';
import { DbAuroraStack } from '../lib/stack/db-aurora-stack';
import * as rds from 'aws-cdk-lib/aws-rds';
import { ShareResourcesStack } from '../lib/stack/share-resources-stack';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { config, envKey, getProcEnv, pjPrefix } from '../test';

let app: cdk.App;
let stack: DbAuroraStack;


beforeEach(() => {
  app = new cdk.App();
  const envTagName = 'Environment';

  // Create a mock stack for resources that need to be in a Stack scope
  const mockResourceStack = new cdk.Stack(app, 'MockResourceStack', {
    env: getProcEnv(),
  });

  // Create the secret in the mock stack instead of directly under the app
  new secretsmanager.CfnSecret(mockResourceStack, `MockNewRelicSecret`, {});

  // Import output from common CDK app
  const shareResources = new ShareResourcesStack(app, `MockShareResources`, {
    pjPrefix,
    notifyEmail: '<EMAIL>',
    domainPrefix: `${pjPrefix}`.toLowerCase(),
    workspaceId: 'test-workspace',
    channelId: 'test-channel',
    urlForCallback: ['http://test/callback'],
    urlForLogout: ['http://test/logout'],
    myVpcCidr: config.VpcParam.cidr,
    myVpcMaxAzs: config.VpcParam.maxAzs,
    myVpcNatGateways: config.VpcParam.natGateways,
    myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
    logRemovalPolicyParam: config.LogRemovalPolicyParam,
    kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
    env: getProcEnv(),
  });

  const vpc = shareResources.myVpc;
  const appKey = shareResources.appKey;
  const alarmTopic = shareResources.alarmTopic;

  stack = new DbAuroraStack(app, `${pjPrefix}-DBAurora`, {
    vpc: vpc,
    vpcSubnets: vpc.selectSubnets({
      subnetGroupName: 'Protected',
    }),
    appServerSecurityGroup: undefined,
    bastionSecurityGroup: undefined,
    appKey: appKey,
    alarmTopic: alarmTopic,
    ...config.AuroraParam,
    env: getProcEnv(),
    terminationProtection: config.AuroraParam.stackTerminationProtection,
  });

  cdk.Tags.of(app).add(envTagName, envKey);
});

describe('DbAuroraStack', () => {
  test('Should match snapshot', () => {
    const template = Template.fromStack(stack);
    const templateJson = template.toJSON();
    expect(templateJson).toMatchSnapshot();
  });

  test('Should create an Aurora cluster', () => {
    const template = Template.fromStack(stack);
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      Engine: Match.stringLikeRegexp('aurora-(mysql|postgresql)'),
      DBClusterParameterGroupName: Match.anyValue(),
      MasterUsername: Match.anyValue(),
    });
  });

  test('Should create CloudWatch alarms for the Aurora cluster', () => {
    const template = Template.fromStack(stack);
    template.resourceCountIs('AWS::CloudWatch::Alarm', 1);
  });

  test('Should enable deletion protection if specified', () => {
    const template = Template.fromStack(stack);
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      DeletionProtection: config.AuroraParam.stackTerminationProtection,
    });
  });

  // should enable aurora export log to cloudwatch
  test('Should enable Aurora export log to CloudWatch', () => {
    const template = Template.fromStack(stack);
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      EnableCloudwatchLogsExports: Match.anyValue(),
    });
  });
  test('Should set backup retention days', () => {
    const template = Template.fromStack(stack);
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      BackupRetentionPeriod: config.AuroraParam.backupRetentionDays,
    });
  });

  test('Should set backup preferred window', () => {
    const template = Template.fromStack(stack);
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      PreferredBackupWindow: config.AuroraParam.backupPreferredWindow,
    });
  });

  test('Should set preferred maintenance window', () => {
    const template = Template.fromStack(stack);
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      PreferredMaintenanceWindow: config.AuroraParam.preferredMaintenanceWindow,
    });
  });

  test('Should enable auto minor version upgrade', () => {
    const template = Template.fromStack(stack);
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      AutoMinorVersionUpgrade: config.AuroraParam.autoMinorVersionUpgrade,
    });
  });

  // Test Aurora PostgreSQL engine
  test('Should create Aurora PostgreSQL cluster when dbVersion is postgres engine', () => {
    // Create a new app for this test to avoid conflicts
    const postgresApp = new cdk.App();
    const postgresEnvTagName = 'Environment';
    cdk.Tags.of(postgresApp).add(postgresEnvTagName, envKey);

    // Create a mock stack for PostgreSQL test resources
    new cdk.Stack(postgresApp, 'PostgresMockStack', {
      env: getProcEnv(),
    });

    // Create a new ShareResourcesStack for this test
    const postgresShareResources = new ShareResourcesStack(postgresApp, `PostgresMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    const vpc = postgresShareResources.myVpc;
    const appKey = postgresShareResources.appKey;
    const alarmTopic = postgresShareResources.alarmTopic;

    const stackPostgres = new DbAuroraStack(postgresApp, `${pjPrefix}-DBAurora-postgress`, {
      vpc: vpc,
      vpcSubnets: vpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      // appServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
      // bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      appServerSecurityGroup: undefined, // TODO: remove later
      bastionSecurityGroup: undefined, // TODO: remove later
      appKey: appKey,
      alarmTopic: alarmTopic,
      ...config.AuroraParam,
      dbVersion: rds.AuroraPostgresEngineVersion.VER_15_4, // This is the key property that needs to be fixed
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
    });

    const template = Template.fromStack(stackPostgres);
    template.hasResourceProperties('AWS::RDS::DBCluster', {
      Engine: 'aurora-postgresql',
    });
  });

  // test property enablePerformanceInsights = true
  // create new stack for this test to avoid conflicts
  test('Should enable Performance Insights when enablePerformanceInsights is true', () => {
    // Create a new app for this test to avoid conflicts
    const postgresApp = new cdk.App();
    const postgresEnvTagName = 'Environment';
    cdk.Tags.of(postgresApp).add(postgresEnvTagName, envKey);

    // Create a mock stack for PostgreSQL test resources
    new cdk.Stack(postgresApp, 'PostgresMockStack', {
      env: getProcEnv(),
    });

    // Create a new ShareResourcesStack for this test
    const postgresShareResources = new ShareResourcesStack(postgresApp, `PostgresMockShareResources`, {
      pjPrefix,
      notifyEmail: '<EMAIL>',
      domainPrefix: `${pjPrefix}`.toLowerCase(),
      workspaceId: 'test-workspace',
      channelId: 'test-channel',
      urlForCallback: ['http://test/callback'],
      urlForLogout: ['http://test/logout'],
      myVpcCidr: config.VpcParam.cidr,
      myVpcMaxAzs: config.VpcParam.maxAzs,
      myVpcNatGateways: config.VpcParam.natGateways,
      myFlowLogBucketLifecycleRule: config.s3AuditLogLifecycleRules,
      logRemovalPolicyParam: config.LogRemovalPolicyParam,
      kmsPendingWindow: config.KmsKeyParam?.pendingWindow,
      env: getProcEnv(),
    });

    const vpc = postgresShareResources.myVpc;
    const appKey = postgresShareResources.appKey;
    const alarmTopic = postgresShareResources.alarmTopic;
    // mock appServerSecurityGroup is SecurityGroup
    const appServerSecurityGroup = new cdk.aws_ec2.SecurityGroup(postgresShareResources, 'MockAppServerSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: true,
      description: 'Mock App Server Security Group',
    });

    const bastionSecurityGroup= new cdk.aws_ec2.SecurityGroup(postgresShareResources, 'MockBastionSecurityGroup', {
      vpc: vpc,
      allowAllOutbound: true,
      description: 'Mock Bastion Security Group',
    });
    // Create a new ShareResourcesStack for this test
    const stackPostgres = new DbAuroraStack(postgresApp, `${pjPrefix}-DBAurora-postgress`, {
      vpc: vpc,
      vpcSubnets: vpc.selectSubnets({
        subnetGroupName: 'Protected',
      }),
      // appServerSecurityGroup: ecs.app.backEcsApps.securityGroupForFargate,
      // bastionSecurityGroup: ecs.app.bastionApp.securityGroup,
      appKey: appKey,
      alarmTopic: alarmTopic,
      ...config.AuroraParam,
      dbVersion: rds.AuroraMysqlEngineVersion.VER_3_04_3, // This is the key property that needs to be fixed
      env: getProcEnv(),
      terminationProtection: config.AuroraParam.stackTerminationProtection,
      enablePerformanceInsights: true, // Enable Performance Insights for this test
      autoMinorVersionUpgrade: undefined,
      backupPreferredWindow: undefined,
      backupRetentionDays: undefined,
      preferredMaintenanceWindow: undefined,
      deletionProtection: undefined,
      appServerSecurityGroup: appServerSecurityGroup,
      bastionSecurityGroup: bastionSecurityGroup,
    });

    const template = Template.fromStack(stackPostgres);
    template.hasResourceProperties('AWS::RDS::DBInstance', {
      EnablePerformanceInsights: true, // Check if Performance Insights is enabled
    });
  });
});